#!/usr/bin/env python3
"""
交易规则获取测试脚本
专门测试交易规则获取失败的原因
"""

import os
import sys
import asyncio
import traceback
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent / "123"
sys.path.insert(0, str(project_root))
os.chdir(project_root)

async def test_single_trading_rule():
    """测试单个交易规则获取"""
    print("🔍 测试单个交易规则获取...")
    
    try:
        # 加载环境变量
        from dotenv import load_dotenv
        load_dotenv()
        
        # 导入交易规则预加载器
        from core.trading_rules_preloader import get_trading_rules_preloader
        preloader = get_trading_rules_preloader()
        
        # 测试获取一个具体的交易规则
        test_symbol = "SPK-USDT"
        test_exchange = "gate"
        test_market = "spot"
        
        print(f"📋 测试获取: {test_symbol}_{test_exchange}_{test_market}")
        
        # 直接调用 get_trading_rule 方法 - 🔥 修复参数顺序和同步调用
        rule = preloader.get_trading_rule(test_exchange, test_symbol, test_market)
        
        if rule:
            print(f"✅ 成功获取交易规则:")
            print(f"   最小订单量: {getattr(rule, 'min_order_amount', 'N/A')}")
            print(f"   最大订单量: {getattr(rule, 'max_order_amount', 'N/A')}")
            print(f"   价格精度: {getattr(rule, 'price_precision', 'N/A')}")
            print(f"   数量精度: {getattr(rule, 'amount_precision', 'N/A')}")
            print(f"   步长: {getattr(rule, 'qty_step', 'N/A')}")
            print(f"   类型: {type(rule)}")
        else:
            print("❌ 获取交易规则失败")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        print(f"详细错误: {traceback.format_exc()}")

async def test_exchange_instance_creation():
    """测试交易所实例创建"""
    print("\n🔍 测试交易所实例创建...")
    
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        # 测试创建 Gate 交易所实例
        from exchanges.gate_exchange import GateExchange
        
        api_key = os.getenv('GATE_API_KEY')
        api_secret = os.getenv('GATE_API_SECRET')
        
        if not api_key or not api_secret:
            print("❌ Gate API 密钥未配置")
            return
            
        print("📋 创建 Gate 交易所实例...")
        gate_instance = GateExchange(api_key, api_secret)
        
        print("✅ Gate 交易所实例创建成功")
        
        # 测试获取交易对信息
        print("📋 测试获取交易对信息...")
        try:
            # 使用超时保护
            symbol_info = await asyncio.wait_for(
                gate_instance.get_symbol_info("SPK-USDT", "spot"),
                timeout=10.0
            )
            
            if symbol_info:
                print(f"✅ 成功获取 SPK-USDT 交易对信息:")
                print(f"   {symbol_info}")
            else:
                print("❌ 获取交易对信息失败")
                
        except asyncio.TimeoutError:
            print("❌ 获取交易对信息超时")
        except Exception as e:
            print(f"❌ 获取交易对信息异常: {e}")
            
    except Exception as e:
        print(f"❌ 交易所实例创建异常: {e}")
        print(f"详细错误: {traceback.format_exc()}")

async def test_global_exchanges():
    """测试全局交易所实例"""
    print("\n🔍 测试全局交易所实例...")
    
    try:
        from core.trading_system_initializer import get_trading_system_initializer
        
        # 获取全局交易所实例
        from core.global_exchange_manager import get_global_exchanges
        exchanges = get_global_exchanges()
        
        if exchanges:
            print(f"✅ 找到全局交易所实例: {list(exchanges.keys())}")
            
            # 测试使用全局实例获取交易规则
            if 'gate' in exchanges:
                gate_exchange = exchanges['gate']
                print("📋 使用全局 Gate 实例测试...")
                
                try:
                    symbol_info = await asyncio.wait_for(
                        gate_exchange.get_symbol_info("SPK-USDT", "spot"),
                        timeout=10.0
                    )
                    
                    if symbol_info:
                        print(f"✅ 全局实例获取交易对信息成功")
                    else:
                        print("❌ 全局实例获取交易对信息失败")
                        
                except Exception as e:
                    print(f"❌ 全局实例测试异常: {e}")
        else:
            print("❌ 未找到全局交易所实例")
            
    except Exception as e:
        print(f"❌ 全局交易所测试异常: {e}")
        print(f"详细错误: {traceback.format_exc()}")

async def main():
    """主测试函数"""
    print("🚀 开始交易规则获取诊断")
    print("=" * 60)
    
    await test_single_trading_rule()
    await test_exchange_instance_creation()
    await test_global_exchanges()
    
    print("=" * 60)
    print("✅ 交易规则诊断完成")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程异常: {e}")
        print(f"详细错误: {traceback.format_exc()}")
